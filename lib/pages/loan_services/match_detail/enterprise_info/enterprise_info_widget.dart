import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:zrreport/common/index.dart';
import 'provider/enterprise_info_provider.dart';
import 'model/enterprise_info.dart';

/// 企业信息页面
class EnterpriseInfoWidget extends ConsumerStatefulWidget {
  /// 企业ID
  final String enterpriseId;

  const EnterpriseInfoWidget({
    super.key,
    required this.enterpriseId,
  });

  @override
  ConsumerState<EnterpriseInfoWidget> createState() =>
      _EnterpriseInfoWidgetState();
}

class _EnterpriseInfoWidgetState extends ConsumerState<EnterpriseInfoWidget> {
  @override
  void initState() {
    super.initState();
    // 页面初始化时获取企业信息
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref
          .read(enterpriseInfoNotifierProvider.notifier)
          .fetchEnterpriseInfo(widget.enterpriseId);
    });
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(enterpriseInfoNotifierProvider);

    if (state.isLoading) {
      return const LoadingWidget();
    }

    if (state.error != null) {
      return ErrorStatusWidget(
        text: state.error!,
        onAttempt: () {
          ref
              .read(enterpriseInfoNotifierProvider.notifier)
              .reload(widget.enterpriseId);
        },
      );
    }

    if (state.enterpriseInfo == null) {
      return const EmptyWidget(text: '暂无企业信息');
    }

    return _buildEnterpriseInfo(state.enterpriseInfo!);
  }

  /// 构建企业信息内容
  Widget _buildEnterpriseInfo(EnterpriseInfo info) {
    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.primary,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          // 企业名称标题
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            child: Text(
              info.enterpriseName ?? '企业名称',
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Colors.white,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          // 企业信息列表
          Container(
            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                bottomLeft: Radius.circular(12),
                bottomRight: Radius.circular(12),
              ),
            ),
            child: Column(
              children: _buildEnterpriseInfoRows(info),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建企业信息行列表
  List<Widget> _buildEnterpriseInfoRows(EnterpriseInfo info) {
    final rows = <Widget>[];
    int index = 0;

    // 法人年龄
    if (info.age != null) {
      rows.add(AdaptiveTextRow(
        label: '法人年龄(岁)',
        value: '${info.age}',
        isOdd: index % 2 == 0,
      ));
      index++;
    } else {
      rows.add(AdaptiveTextRow(
        label: '法人年龄(岁)',
        value: '-',
        isOdd: index % 2 == 0,
      ));
      index++;
    }

    // 法人股份占比
    if (info.stockPercent != null && info.stockPercent!.isNotEmpty) {
      rows.add(AdaptiveTextRow(
        label: '法人股份占比(%)',
        value: info.stockPercent!,
        isOdd: index % 2 == 0,
      ));
      index++;
    } else {
      rows.add(AdaptiveTextRow(
        label: '法人股份占比(%)',
        value: '-',
        isOdd: index % 2 == 0,
      ));
      index++;
    }

    // 统一信用代码
    if (info.creditCode != null && info.creditCode!.isNotEmpty) {
      rows.add(AdaptiveTextRow(
        label: '统一信用代码',
        value: info.creditCode!,
        isOdd: index % 2 == 0,
        canCopy: true,
      ));
      index++;
    }

    // 当前法人姓名
    if (info.legalPerson != null && info.legalPerson!.isNotEmpty) {
      rows.add(AdaptiveTextRow(
        label: '当前法人姓名',
        value: info.legalPerson!,
        isOdd: index % 2 == 0,
      ));
      index++;
    }

    // 基本户 (开户银行)
    if (info.openAccountBank != null && info.openAccountBank!.isNotEmpty) {
      rows.add(AdaptiveTextRow(
        label: '基本户',
        value: info.openAccountBank!,
        isOdd: index % 2 == 0,
      ));
      index++;
    } else {
      rows.add(AdaptiveTextRow(
        label: '基本户',
        value: '-',
        isOdd: index % 2 == 0,
      ));
      index++;
    }

    // 注册类型
    if (info.loginRegisterType != null && info.loginRegisterType!.isNotEmpty) {
      rows.add(AdaptiveTextRow(
        label: '注册类型',
        value: info.loginRegisterType!,
        isOdd: index % 2 == 0,
      ));
      index++;
    }

    // 注册区域 (注册地址)
    if (info.registerAddress != null && info.registerAddress!.isNotEmpty) {
      rows.add(AdaptiveTextRow(
        label: '注册区域',
        value: info.registerAddress!,
        isOdd: index % 2 == 0,
        maxLines: 3,
      ));
      index++;
    }

    // 成立日期
    if (info.registerDate != null && info.registerDate!.isNotEmpty) {
      rows.add(AdaptiveTextRow(
        label: '成立日期',
        value: info.registerDate!,
        isOdd: index % 2 == 0,
      ));
      index++;
    }

    // 成立时长
    if (info.registerMonth != null) {
      rows.add(AdaptiveTextRow(
        label: '成立时长',
        value: '${info.registerMonth}个月',
        isOdd: index % 2 == 0,
      ));
      index++;
    }

    // 行业分类
    if (info.industry != null && info.industry!.isNotEmpty) {
      rows.add(AdaptiveTextRow(
        label: '行业分类',
        value: info.industry!,
        isOdd: index % 2 == 0,
      ));
      index++;
    }

    // 法人是否占股
    rows.add(AdaptiveTextRow(
      label: '法人是否占股',
      value: (info.stockStatus == true) ? '是' : '否',
      isOdd: index % 2 == 0,
    ));

    return rows;
  }
}
