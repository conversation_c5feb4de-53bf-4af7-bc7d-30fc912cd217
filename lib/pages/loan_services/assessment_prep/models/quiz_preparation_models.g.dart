// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'quiz_preparation_models.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_AssessmentPreparationParams _$AssessmentPreparationParamsFromJson(
        Map<String, dynamic> json) =>
    _AssessmentPreparationParams(
      creditCode: json['creditCode'] as String,
      enterpriseName: json['enterpriseName'] as String,
      personStand: json['personStand'] as String,
      age: json['age'] as String,
      areaId: json['areaId'] as String,
      channelType: json['channelType'] as String,
    );

Map<String, dynamic> _$AssessmentPreparationParamsToJson(
        _AssessmentPreparationParams instance) =>
    <String, dynamic>{
      'creditCode': instance.creditCode,
      'enterpriseName': instance.enterpriseName,
      'personStand': instance.personStand,
      'age': instance.age,
      'areaId': instance.areaId,
      'channelType': instance.channelType,
    };

_AssessmentPreparationRequest _$AssessmentPreparationRequestFromJson(
        Map<String, dynamic> json) =>
    _AssessmentPreparationRequest(
      matchStep: (json['matchStep'] as num?)?.toInt() ?? 0,
      personStand: json['personStand'] as String,
      age: json['age'] as String,
      creditCode: json['creditCode'] as String,
      enterpriseName: json['enterpriseName'] as String,
      areaId: json['areaId'] as String,
      channelType: json['channelType'] as String? ?? "routine",
    );

Map<String, dynamic> _$AssessmentPreparationRequestToJson(
        _AssessmentPreparationRequest instance) =>
    <String, dynamic>{
      'matchStep': instance.matchStep,
      'personStand': instance.personStand,
      'age': instance.age,
      'creditCode': instance.creditCode,
      'enterpriseName': instance.enterpriseName,
      'areaId': instance.areaId,
      'channelType': instance.channelType,
    };

_AssessmentPreparationData _$AssessmentPreparationDataFromJson(
        Map<String, dynamic> json) =>
    _AssessmentPreparationData(
      enterpriseId: json['enterpriseId'] as String?,
      creditCode: json['creditCode'] as String?,
      id: json['id'] as String,
      matchStatus: (json['matchStatus'] as num).toInt(),
    );

Map<String, dynamic> _$AssessmentPreparationDataToJson(
        _AssessmentPreparationData instance) =>
    <String, dynamic>{
      'enterpriseId': instance.enterpriseId,
      'creditCode': instance.creditCode,
      'id': instance.id,
      'matchStatus': instance.matchStatus,
    };
