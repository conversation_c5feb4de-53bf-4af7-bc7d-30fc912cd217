import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:zrreport/common/models/base_response.dart';

part 'quiz_preparation_models.freezed.dart';
part 'quiz_preparation_models.g.dart';

/// 评估准备页面参数
@freezed
abstract class AssessmentPreparationParams with _$AssessmentPreparationParams {
  const factory AssessmentPreparationParams({
    /// 统一社会信用代码
    required String creditCode,
    /// 企业名称
    required String enterpriseName,
    /// 申请人身份
    required String personStand,
    /// 年龄
    required String age,
    /// 地区ID
    required String areaId,
    /// 渠道类型
    required String channelType,
  }) = _AssessmentPreparationParams;

  factory AssessmentPreparationParams.fromJson(Map<String, dynamic> json) =>
      _$AssessmentPreparationParamsFromJson(json);
}

/// 评估准备请求参数
@freezed
abstract class AssessmentPreparationRequest with _$AssessmentPreparationRequest {
  const factory AssessmentPreparationRequest({
    /// 匹配步骤，默认为0
    @Default(0) int matchStep,
    /// 申请人身份 1
    required String personStand,
    /// 年龄
    required String age,
    /// 统一社会信用代码
    required String creditCode,
    /// 企业名称
    required String enterpriseName,
    /// 地区ID
    required String areaId,
    /// 渠道类型，默认为routine
    @Default("routine") String channelType,
  }) = _AssessmentPreparationRequest;

  factory AssessmentPreparationRequest.fromJson(Map<String, dynamic> json) =>
      _$AssessmentPreparationRequestFromJson(json);
}

/// 评估准备响应数据
@freezed
abstract class AssessmentPreparationData with _$AssessmentPreparationData {
  const factory AssessmentPreparationData({
    /// 企业ID
    String? enterpriseId,
    /// 统一社会信用代码
    String? creditCode,
    /// 匹配ID
    required String id,
    /// 匹配状态
    required int matchStatus,
  }) = _AssessmentPreparationData;

  factory AssessmentPreparationData.fromJson(Map<String, dynamic> json) =>
      _$AssessmentPreparationDataFromJson(json);
}

/// 评估准备响应
typedef AssessmentPreparationResponse = BaseResponse<AssessmentPreparationData>;

/// 申请人身份枚举
enum ApplicantType {
  legalPerson('1', '法人'),
  shareholder('2', '股东'),
  majorShareholder('3', '最大股东'),
  other('4', '其他');

  const ApplicantType(this.value, this.displayName);
  final String value;
  final String displayName;
}
