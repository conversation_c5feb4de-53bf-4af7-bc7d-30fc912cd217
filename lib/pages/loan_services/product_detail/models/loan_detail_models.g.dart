// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'loan_detail_models.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_ProductDetailModel _$ProductDetailModelFromJson(Map<String, dynamic> json) =>
    _ProductDetailModel(
      id: json['id'] as String,
      cateId: json['cateId'] as String?,
      bankId: json['bankId'] as String,
      adminId: json['adminId'] as String,
      deptId: json['deptId'] as String,
      pic: json['pic'] as String?,
      name: json['name'] as String,
      productSn: json['productSn'] as String?,
      type: (json['type'] as num).toInt(),
      keywords: json['keywords'] as String?,
      bankName: json['bankName'] as String,
      minQuota: (json['minQuota'] as num).toInt(),
      maxQuota: (json['maxQuota'] as num).toInt(),
      minAnnualRate: json['minAnnualRate'] as String,
      maxAnnualRate: json['maxAnnualRate'] as String,
      loanTerm: json['loanTerm'] as String,
      repaymentType: json['repaymentType'] as String,
      repaymentName: json['repaymentName'] as String?,
      advantageDataJson: json['advantageDataJson'] as String?,
      loanType: (json['loanType'] as num).toInt(),
      legalApplyRequire: json['legalApplyRequire'] as String?,
      companyTaxInfo: json['companyTaxInfo'] as String?,
      companyBizStatus: json['companyBizStatus'] as String?,
      companyBankFlow: json['companyBankFlow'] as String?,
      corporateAge: json['corporateAge'] as String?,
      corporateMaxAge: json['corporateMaxAge'] as String?,
      corporateChangeTime: json['corporateChangeTime'] as String?,
      corporateSharesRatio: json['corporateSharesRatio'] as String?,
      companyCreateTime: json['companyCreateTime'] as String?,
      companyTaxRating: json['companyTaxRating'] as String?,
      companyTaxStatus: json['companyTaxStatus'] as String?,
      companyReceiptRequire: json['companyReceiptRequire'] as String?,
      accessAreaType: (json['accessAreaType'] as num).toInt(),
      publishStatus: (json['publishStatus'] as num).toInt(),
      verifyStatus: json['verifyStatus'] as String?,
      note: json['note'] as String?,
      createTime: json['createTime'] as String,
      updateBy: json['updateBy'] as String?,
      matchType: json['matchType'] as String?,
      creditRequire: json['creditRequire'] as String?,
      otherRequire: json['otherRequire'] as String?,
      corporateRequire: json['corporateRequire'] as String?,
      companyApplyRequire: json['companyApplyRequire'] as String?,
      accessIndustry: json['accessIndustry'] as String?,
      prohibitIndustry: json['prohibitIndustry'] as String?,
      requiredMaterials: json['requiredMaterials'] as String?,
      applyDesc: json['applyDesc'] as String?,
      channelCode: json['channelCode'] as String?,
      intoElement: json['intoElement'] as String?,
      forbidIndustryList: (json['forbidIndustryList'] as List<dynamic>?)
              ?.map((e) =>
                  ForbidIndustryModel.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      customRequireList: (json['customRequireList'] as List<dynamic>?)
              ?.map(
                  (e) => CustomRequireModel.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      treeSelect: json['treeSelect'] as List<dynamic>? ?? const [],
      haveQuestion: json['haveQuestion'] as bool? ?? false,
    );

Map<String, dynamic> _$ProductDetailModelToJson(_ProductDetailModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'cateId': instance.cateId,
      'bankId': instance.bankId,
      'adminId': instance.adminId,
      'deptId': instance.deptId,
      'pic': instance.pic,
      'name': instance.name,
      'productSn': instance.productSn,
      'type': instance.type,
      'keywords': instance.keywords,
      'bankName': instance.bankName,
      'minQuota': instance.minQuota,
      'maxQuota': instance.maxQuota,
      'minAnnualRate': instance.minAnnualRate,
      'maxAnnualRate': instance.maxAnnualRate,
      'loanTerm': instance.loanTerm,
      'repaymentType': instance.repaymentType,
      'repaymentName': instance.repaymentName,
      'advantageDataJson': instance.advantageDataJson,
      'loanType': instance.loanType,
      'legalApplyRequire': instance.legalApplyRequire,
      'companyTaxInfo': instance.companyTaxInfo,
      'companyBizStatus': instance.companyBizStatus,
      'companyBankFlow': instance.companyBankFlow,
      'corporateAge': instance.corporateAge,
      'corporateMaxAge': instance.corporateMaxAge,
      'corporateChangeTime': instance.corporateChangeTime,
      'corporateSharesRatio': instance.corporateSharesRatio,
      'companyCreateTime': instance.companyCreateTime,
      'companyTaxRating': instance.companyTaxRating,
      'companyTaxStatus': instance.companyTaxStatus,
      'companyReceiptRequire': instance.companyReceiptRequire,
      'accessAreaType': instance.accessAreaType,
      'publishStatus': instance.publishStatus,
      'verifyStatus': instance.verifyStatus,
      'note': instance.note,
      'createTime': instance.createTime,
      'updateBy': instance.updateBy,
      'matchType': instance.matchType,
      'creditRequire': instance.creditRequire,
      'otherRequire': instance.otherRequire,
      'corporateRequire': instance.corporateRequire,
      'companyApplyRequire': instance.companyApplyRequire,
      'accessIndustry': instance.accessIndustry,
      'prohibitIndustry': instance.prohibitIndustry,
      'requiredMaterials': instance.requiredMaterials,
      'applyDesc': instance.applyDesc,
      'channelCode': instance.channelCode,
      'intoElement': instance.intoElement,
      'forbidIndustryList': instance.forbidIndustryList,
      'customRequireList': instance.customRequireList,
      'treeSelect': instance.treeSelect,
      'haveQuestion': instance.haveQuestion,
    };

_ForbidIndustryModel _$ForbidIndustryModelFromJson(Map<String, dynamic> json) =>
    _ForbidIndustryModel(
      id: json['id'] as String,
      industryId: json['industryId'] as String,
      industryCode: json['industryCode'] as String,
      industryName: json['industryName'] as String,
      relation: json['relation'] as String,
      type: (json['type'] as num).toInt(),
    );

Map<String, dynamic> _$ForbidIndustryModelToJson(
        _ForbidIndustryModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'industryId': instance.industryId,
      'industryCode': instance.industryCode,
      'industryName': instance.industryName,
      'relation': instance.relation,
      'type': instance.type,
    };

_CustomRequireModel _$CustomRequireModelFromJson(Map<String, dynamic> json) =>
    _CustomRequireModel(
      title: json['title'] as String,
      content: json['content'] as String,
    );

Map<String, dynamic> _$CustomRequireModelToJson(_CustomRequireModel instance) =>
    <String, dynamic>{
      'title': instance.title,
      'content': instance.content,
    };
