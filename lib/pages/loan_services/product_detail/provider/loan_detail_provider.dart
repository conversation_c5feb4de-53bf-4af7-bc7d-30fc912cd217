import 'package:flutter/material.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:scrollable_positioned_list/scrollable_positioned_list.dart';
import '../api/loan_detail_api.dart';
import '../models/loan_detail_models.dart';
import 'loan_detail_state.dart';

part 'loan_detail_provider.g.dart';

/// 贷款商品详情页面状态管理
@riverpod
class LoanDetailNotifier extends _$LoanDetailNotifier {
  /// ScrollablePositionedList的控制器
  final ItemScrollController itemScrollController = ItemScrollController();

  /// 监听item位置变化
  final ItemPositionsListener itemPositionsListener =
      ItemPositionsListener.create();

  @override
  LoanDetailState build() {
    return const LoanDetailState();
  }

  /// 初始化页面数据
  Future<void> initialize(String productId) async {
    state = state.copyWith(isLoading: true, error: null);
    
    try {
      final response = await LoanDetailApi.getLoanDetail(productId);
      
      if (response.code == 200 && response.data != null) {
        // 动态生成导航项（包含自定义要求）
        final navigationItems = _generateNavigationItems(response.data!);

        state = state.copyWith(
          isLoading: false,
          loanDetail: response.data,
          navigationItems: navigationItems,
        );
      } else {
        state = state.copyWith(
          isLoading: false,
          error: response.message,
        );
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// 切换导航栏展开状态
  void toggleNavigation() {
    state = state.copyWith(
      isNavigationExpanded: !state.isNavigationExpanded,
    );
  }

  /// 选择导航项
  void selectNavigation(String key) {
    final updatedItems = state.navigationItems.map((item) {
      return item.copyWith(isSelected: item.key == key);
    }).toList();
    
    final selectedItem = updatedItems.firstWhere((item) => item.key == key);
    
    state = state.copyWith(
      selectedNavigation: selectedItem.title,
      navigationItems: updatedItems,
    );
  }

  /// 滚动到指定section
  void scrollToSection(String key) {
    final sectionItems = state.sectionItems;
    final index = sectionItems.indexWhere((item) => item.key == key);

    if (index != -1) {
      itemScrollController.scrollTo(
        index: index,
        duration: const Duration(milliseconds: 500),
        curve: Curves.easeInOut,
        alignment: 0.0, // 顶部对齐
      );
    }
    selectNavigation(key);
  }

  /// 重新加载数据
  Future<void> reload(String productId) async {
    await initialize(productId);
  }

  /// 监听item位置变化，用于同步左侧导航
  void onItemPositionsChanged() {
    final positions = itemPositionsListener.itemPositions.value;
    if (positions.isEmpty) return;

    // 找到最可见的section
    ItemPosition? mostVisiblePosition;
    double maxVisibleRatio = 0.0;

    for (final position in positions) {
      // 计算可见比例
      final visibleRatio = _calculateVisibleRatio(position);
      if (visibleRatio > maxVisibleRatio) {
        maxVisibleRatio = visibleRatio;
        mostVisiblePosition = position;
      }
    }

    if (mostVisiblePosition != null) {
      final sectionItems = state.sectionItems;
      if (mostVisiblePosition.index < sectionItems.length) {
        final currentSection = sectionItems[mostVisiblePosition.index];
        final currentTitle = state.navigationItems
            .firstWhere((item) => item.key == currentSection.key)
            .title;

        if (state.selectedNavigation != currentTitle) {
          selectNavigation(currentSection.key);
        }
      }
    }
  }

  /// 计算item的可见比例
  double _calculateVisibleRatio(ItemPosition position) {
    // 如果item完全在视口内
    if (position.itemLeadingEdge >= 0 && position.itemTrailingEdge <= 1) {
      return 1.0;
    }

    // 如果item部分可见
    final visibleLeading = position.itemLeadingEdge.clamp(0.0, 1.0);
    final visibleTrailing = position.itemTrailingEdge.clamp(0.0, 1.0);

    return visibleTrailing - visibleLeading;
  }

  /// 动态生成导航项（包含自定义要求）
  List<NavigationItem> _generateNavigationItems(ProductDetailModel loanDetail) {
    final items = <NavigationItem>[
      const NavigationItem(title: '个人要求', key: 'personal', isSelected: true),
      const NavigationItem(title: '企业要求', key: 'company'),
      const NavigationItem(title: '征信要求', key: 'credit'),
      const NavigationItem(title: '准入地区', key: 'area'),
      const NavigationItem(title: '禁入行业', key: 'industry'),
    ];

    // 如果有自定义要求，添加对应的导航项
    if (loanDetail.customRequireList.isNotEmpty) {
      for (int i = 0; i < loanDetail.customRequireList.length; i++) {
        final customRequire = loanDetail.customRequireList[i];
        items.add(NavigationItem(
          title: customRequire.title,
          key: 'custom_$i',
        ));
      }
    }

    return items;
  }

  /// 联系客服
  void contactCustomerService() {
    // TODO: 实现联系客服功能
  }
}
