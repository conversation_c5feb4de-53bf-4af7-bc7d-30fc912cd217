// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'quiz_preparation_models.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$AssessmentPreparationParams {
  /// 统一社会信用代码
  String get creditCode;

  /// 企业名称
  String get enterpriseName;

  /// 申请人身份
  String get personStand;

  /// 年龄
  String get age;

  /// 地区ID
  String get areaId;

  /// 渠道类型
  String get channelType;

  /// Create a copy of AssessmentPreparationParams
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $AssessmentPreparationParamsCopyWith<AssessmentPreparationParams>
      get copyWith => _$AssessmentPreparationParamsCopyWithImpl<
              AssessmentPreparationParams>(
          this as AssessmentPreparationParams, _$identity);

  /// Serializes this AssessmentPreparationParams to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is AssessmentPreparationParams &&
            (identical(other.creditCode, creditCode) ||
                other.creditCode == creditCode) &&
            (identical(other.enterpriseName, enterpriseName) ||
                other.enterpriseName == enterpriseName) &&
            (identical(other.personStand, personStand) ||
                other.personStand == personStand) &&
            (identical(other.age, age) || other.age == age) &&
            (identical(other.areaId, areaId) || other.areaId == areaId) &&
            (identical(other.channelType, channelType) ||
                other.channelType == channelType));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, creditCode, enterpriseName,
      personStand, age, areaId, channelType);

  @override
  String toString() {
    return 'AssessmentPreparationParams(creditCode: $creditCode, enterpriseName: $enterpriseName, personStand: $personStand, age: $age, areaId: $areaId, channelType: $channelType)';
  }
}

/// @nodoc
abstract mixin class $AssessmentPreparationParamsCopyWith<$Res> {
  factory $AssessmentPreparationParamsCopyWith(
          AssessmentPreparationParams value,
          $Res Function(AssessmentPreparationParams) _then) =
      _$AssessmentPreparationParamsCopyWithImpl;
  @useResult
  $Res call(
      {String creditCode,
      String enterpriseName,
      String personStand,
      String age,
      String areaId,
      String channelType});
}

/// @nodoc
class _$AssessmentPreparationParamsCopyWithImpl<$Res>
    implements $AssessmentPreparationParamsCopyWith<$Res> {
  _$AssessmentPreparationParamsCopyWithImpl(this._self, this._then);

  final AssessmentPreparationParams _self;
  final $Res Function(AssessmentPreparationParams) _then;

  /// Create a copy of AssessmentPreparationParams
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? creditCode = null,
    Object? enterpriseName = null,
    Object? personStand = null,
    Object? age = null,
    Object? areaId = null,
    Object? channelType = null,
  }) {
    return _then(_self.copyWith(
      creditCode: null == creditCode
          ? _self.creditCode
          : creditCode // ignore: cast_nullable_to_non_nullable
              as String,
      enterpriseName: null == enterpriseName
          ? _self.enterpriseName
          : enterpriseName // ignore: cast_nullable_to_non_nullable
              as String,
      personStand: null == personStand
          ? _self.personStand
          : personStand // ignore: cast_nullable_to_non_nullable
              as String,
      age: null == age
          ? _self.age
          : age // ignore: cast_nullable_to_non_nullable
              as String,
      areaId: null == areaId
          ? _self.areaId
          : areaId // ignore: cast_nullable_to_non_nullable
              as String,
      channelType: null == channelType
          ? _self.channelType
          : channelType // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _AssessmentPreparationParams implements AssessmentPreparationParams {
  const _AssessmentPreparationParams(
      {required this.creditCode,
      required this.enterpriseName,
      required this.personStand,
      required this.age,
      required this.areaId,
      required this.channelType});
  factory _AssessmentPreparationParams.fromJson(Map<String, dynamic> json) =>
      _$AssessmentPreparationParamsFromJson(json);

  /// 统一社会信用代码
  @override
  final String creditCode;

  /// 企业名称
  @override
  final String enterpriseName;

  /// 申请人身份
  @override
  final String personStand;

  /// 年龄
  @override
  final String age;

  /// 地区ID
  @override
  final String areaId;

  /// 渠道类型
  @override
  final String channelType;

  /// Create a copy of AssessmentPreparationParams
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$AssessmentPreparationParamsCopyWith<_AssessmentPreparationParams>
      get copyWith => __$AssessmentPreparationParamsCopyWithImpl<
          _AssessmentPreparationParams>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$AssessmentPreparationParamsToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _AssessmentPreparationParams &&
            (identical(other.creditCode, creditCode) ||
                other.creditCode == creditCode) &&
            (identical(other.enterpriseName, enterpriseName) ||
                other.enterpriseName == enterpriseName) &&
            (identical(other.personStand, personStand) ||
                other.personStand == personStand) &&
            (identical(other.age, age) || other.age == age) &&
            (identical(other.areaId, areaId) || other.areaId == areaId) &&
            (identical(other.channelType, channelType) ||
                other.channelType == channelType));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, creditCode, enterpriseName,
      personStand, age, areaId, channelType);

  @override
  String toString() {
    return 'AssessmentPreparationParams(creditCode: $creditCode, enterpriseName: $enterpriseName, personStand: $personStand, age: $age, areaId: $areaId, channelType: $channelType)';
  }
}

/// @nodoc
abstract mixin class _$AssessmentPreparationParamsCopyWith<$Res>
    implements $AssessmentPreparationParamsCopyWith<$Res> {
  factory _$AssessmentPreparationParamsCopyWith(
          _AssessmentPreparationParams value,
          $Res Function(_AssessmentPreparationParams) _then) =
      __$AssessmentPreparationParamsCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String creditCode,
      String enterpriseName,
      String personStand,
      String age,
      String areaId,
      String channelType});
}

/// @nodoc
class __$AssessmentPreparationParamsCopyWithImpl<$Res>
    implements _$AssessmentPreparationParamsCopyWith<$Res> {
  __$AssessmentPreparationParamsCopyWithImpl(this._self, this._then);

  final _AssessmentPreparationParams _self;
  final $Res Function(_AssessmentPreparationParams) _then;

  /// Create a copy of AssessmentPreparationParams
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? creditCode = null,
    Object? enterpriseName = null,
    Object? personStand = null,
    Object? age = null,
    Object? areaId = null,
    Object? channelType = null,
  }) {
    return _then(_AssessmentPreparationParams(
      creditCode: null == creditCode
          ? _self.creditCode
          : creditCode // ignore: cast_nullable_to_non_nullable
              as String,
      enterpriseName: null == enterpriseName
          ? _self.enterpriseName
          : enterpriseName // ignore: cast_nullable_to_non_nullable
              as String,
      personStand: null == personStand
          ? _self.personStand
          : personStand // ignore: cast_nullable_to_non_nullable
              as String,
      age: null == age
          ? _self.age
          : age // ignore: cast_nullable_to_non_nullable
              as String,
      areaId: null == areaId
          ? _self.areaId
          : areaId // ignore: cast_nullable_to_non_nullable
              as String,
      channelType: null == channelType
          ? _self.channelType
          : channelType // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
mixin _$AssessmentPreparationRequest {
  /// 匹配步骤，默认为0
  int get matchStep;

  /// 申请人身份 1
  String get personStand;

  /// 年龄
  String get age;

  /// 统一社会信用代码
  String get creditCode;

  /// 企业名称
  String get enterpriseName;

  /// 地区ID
  String get areaId;

  /// 渠道类型，默认为routine
  String get channelType;

  /// Create a copy of AssessmentPreparationRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $AssessmentPreparationRequestCopyWith<AssessmentPreparationRequest>
      get copyWith => _$AssessmentPreparationRequestCopyWithImpl<
              AssessmentPreparationRequest>(
          this as AssessmentPreparationRequest, _$identity);

  /// Serializes this AssessmentPreparationRequest to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is AssessmentPreparationRequest &&
            (identical(other.matchStep, matchStep) ||
                other.matchStep == matchStep) &&
            (identical(other.personStand, personStand) ||
                other.personStand == personStand) &&
            (identical(other.age, age) || other.age == age) &&
            (identical(other.creditCode, creditCode) ||
                other.creditCode == creditCode) &&
            (identical(other.enterpriseName, enterpriseName) ||
                other.enterpriseName == enterpriseName) &&
            (identical(other.areaId, areaId) || other.areaId == areaId) &&
            (identical(other.channelType, channelType) ||
                other.channelType == channelType));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, matchStep, personStand, age,
      creditCode, enterpriseName, areaId, channelType);

  @override
  String toString() {
    return 'AssessmentPreparationRequest(matchStep: $matchStep, personStand: $personStand, age: $age, creditCode: $creditCode, enterpriseName: $enterpriseName, areaId: $areaId, channelType: $channelType)';
  }
}

/// @nodoc
abstract mixin class $AssessmentPreparationRequestCopyWith<$Res> {
  factory $AssessmentPreparationRequestCopyWith(
          AssessmentPreparationRequest value,
          $Res Function(AssessmentPreparationRequest) _then) =
      _$AssessmentPreparationRequestCopyWithImpl;
  @useResult
  $Res call(
      {int matchStep,
      String personStand,
      String age,
      String creditCode,
      String enterpriseName,
      String areaId,
      String channelType});
}

/// @nodoc
class _$AssessmentPreparationRequestCopyWithImpl<$Res>
    implements $AssessmentPreparationRequestCopyWith<$Res> {
  _$AssessmentPreparationRequestCopyWithImpl(this._self, this._then);

  final AssessmentPreparationRequest _self;
  final $Res Function(AssessmentPreparationRequest) _then;

  /// Create a copy of AssessmentPreparationRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? matchStep = null,
    Object? personStand = null,
    Object? age = null,
    Object? creditCode = null,
    Object? enterpriseName = null,
    Object? areaId = null,
    Object? channelType = null,
  }) {
    return _then(_self.copyWith(
      matchStep: null == matchStep
          ? _self.matchStep
          : matchStep // ignore: cast_nullable_to_non_nullable
              as int,
      personStand: null == personStand
          ? _self.personStand
          : personStand // ignore: cast_nullable_to_non_nullable
              as String,
      age: null == age
          ? _self.age
          : age // ignore: cast_nullable_to_non_nullable
              as String,
      creditCode: null == creditCode
          ? _self.creditCode
          : creditCode // ignore: cast_nullable_to_non_nullable
              as String,
      enterpriseName: null == enterpriseName
          ? _self.enterpriseName
          : enterpriseName // ignore: cast_nullable_to_non_nullable
              as String,
      areaId: null == areaId
          ? _self.areaId
          : areaId // ignore: cast_nullable_to_non_nullable
              as String,
      channelType: null == channelType
          ? _self.channelType
          : channelType // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _AssessmentPreparationRequest implements AssessmentPreparationRequest {
  const _AssessmentPreparationRequest(
      {this.matchStep = 0,
      required this.personStand,
      required this.age,
      required this.creditCode,
      required this.enterpriseName,
      required this.areaId,
      this.channelType = "routine"});
  factory _AssessmentPreparationRequest.fromJson(Map<String, dynamic> json) =>
      _$AssessmentPreparationRequestFromJson(json);

  /// 匹配步骤，默认为0
  @override
  @JsonKey()
  final int matchStep;

  /// 申请人身份 1
  @override
  final String personStand;

  /// 年龄
  @override
  final String age;

  /// 统一社会信用代码
  @override
  final String creditCode;

  /// 企业名称
  @override
  final String enterpriseName;

  /// 地区ID
  @override
  final String areaId;

  /// 渠道类型，默认为routine
  @override
  @JsonKey()
  final String channelType;

  /// Create a copy of AssessmentPreparationRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$AssessmentPreparationRequestCopyWith<_AssessmentPreparationRequest>
      get copyWith => __$AssessmentPreparationRequestCopyWithImpl<
          _AssessmentPreparationRequest>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$AssessmentPreparationRequestToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _AssessmentPreparationRequest &&
            (identical(other.matchStep, matchStep) ||
                other.matchStep == matchStep) &&
            (identical(other.personStand, personStand) ||
                other.personStand == personStand) &&
            (identical(other.age, age) || other.age == age) &&
            (identical(other.creditCode, creditCode) ||
                other.creditCode == creditCode) &&
            (identical(other.enterpriseName, enterpriseName) ||
                other.enterpriseName == enterpriseName) &&
            (identical(other.areaId, areaId) || other.areaId == areaId) &&
            (identical(other.channelType, channelType) ||
                other.channelType == channelType));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, matchStep, personStand, age,
      creditCode, enterpriseName, areaId, channelType);

  @override
  String toString() {
    return 'AssessmentPreparationRequest(matchStep: $matchStep, personStand: $personStand, age: $age, creditCode: $creditCode, enterpriseName: $enterpriseName, areaId: $areaId, channelType: $channelType)';
  }
}

/// @nodoc
abstract mixin class _$AssessmentPreparationRequestCopyWith<$Res>
    implements $AssessmentPreparationRequestCopyWith<$Res> {
  factory _$AssessmentPreparationRequestCopyWith(
          _AssessmentPreparationRequest value,
          $Res Function(_AssessmentPreparationRequest) _then) =
      __$AssessmentPreparationRequestCopyWithImpl;
  @override
  @useResult
  $Res call(
      {int matchStep,
      String personStand,
      String age,
      String creditCode,
      String enterpriseName,
      String areaId,
      String channelType});
}

/// @nodoc
class __$AssessmentPreparationRequestCopyWithImpl<$Res>
    implements _$AssessmentPreparationRequestCopyWith<$Res> {
  __$AssessmentPreparationRequestCopyWithImpl(this._self, this._then);

  final _AssessmentPreparationRequest _self;
  final $Res Function(_AssessmentPreparationRequest) _then;

  /// Create a copy of AssessmentPreparationRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? matchStep = null,
    Object? personStand = null,
    Object? age = null,
    Object? creditCode = null,
    Object? enterpriseName = null,
    Object? areaId = null,
    Object? channelType = null,
  }) {
    return _then(_AssessmentPreparationRequest(
      matchStep: null == matchStep
          ? _self.matchStep
          : matchStep // ignore: cast_nullable_to_non_nullable
              as int,
      personStand: null == personStand
          ? _self.personStand
          : personStand // ignore: cast_nullable_to_non_nullable
              as String,
      age: null == age
          ? _self.age
          : age // ignore: cast_nullable_to_non_nullable
              as String,
      creditCode: null == creditCode
          ? _self.creditCode
          : creditCode // ignore: cast_nullable_to_non_nullable
              as String,
      enterpriseName: null == enterpriseName
          ? _self.enterpriseName
          : enterpriseName // ignore: cast_nullable_to_non_nullable
              as String,
      areaId: null == areaId
          ? _self.areaId
          : areaId // ignore: cast_nullable_to_non_nullable
              as String,
      channelType: null == channelType
          ? _self.channelType
          : channelType // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
mixin _$AssessmentPreparationData {
  /// 企业ID
  String? get enterpriseId;

  /// 统一社会信用代码
  String? get creditCode;

  /// 匹配ID
  String get id;

  /// 匹配状态
  int get matchStatus;

  /// Create a copy of AssessmentPreparationData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $AssessmentPreparationDataCopyWith<AssessmentPreparationData> get copyWith =>
      _$AssessmentPreparationDataCopyWithImpl<AssessmentPreparationData>(
          this as AssessmentPreparationData, _$identity);

  /// Serializes this AssessmentPreparationData to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is AssessmentPreparationData &&
            (identical(other.enterpriseId, enterpriseId) ||
                other.enterpriseId == enterpriseId) &&
            (identical(other.creditCode, creditCode) ||
                other.creditCode == creditCode) &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.matchStatus, matchStatus) ||
                other.matchStatus == matchStatus));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, enterpriseId, creditCode, id, matchStatus);

  @override
  String toString() {
    return 'AssessmentPreparationData(enterpriseId: $enterpriseId, creditCode: $creditCode, id: $id, matchStatus: $matchStatus)';
  }
}

/// @nodoc
abstract mixin class $AssessmentPreparationDataCopyWith<$Res> {
  factory $AssessmentPreparationDataCopyWith(AssessmentPreparationData value,
          $Res Function(AssessmentPreparationData) _then) =
      _$AssessmentPreparationDataCopyWithImpl;
  @useResult
  $Res call(
      {String? enterpriseId, String? creditCode, String id, int matchStatus});
}

/// @nodoc
class _$AssessmentPreparationDataCopyWithImpl<$Res>
    implements $AssessmentPreparationDataCopyWith<$Res> {
  _$AssessmentPreparationDataCopyWithImpl(this._self, this._then);

  final AssessmentPreparationData _self;
  final $Res Function(AssessmentPreparationData) _then;

  /// Create a copy of AssessmentPreparationData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? enterpriseId = freezed,
    Object? creditCode = freezed,
    Object? id = null,
    Object? matchStatus = null,
  }) {
    return _then(_self.copyWith(
      enterpriseId: freezed == enterpriseId
          ? _self.enterpriseId
          : enterpriseId // ignore: cast_nullable_to_non_nullable
              as String?,
      creditCode: freezed == creditCode
          ? _self.creditCode
          : creditCode // ignore: cast_nullable_to_non_nullable
              as String?,
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      matchStatus: null == matchStatus
          ? _self.matchStatus
          : matchStatus // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _AssessmentPreparationData implements AssessmentPreparationData {
  const _AssessmentPreparationData(
      {this.enterpriseId,
      this.creditCode,
      required this.id,
      required this.matchStatus});
  factory _AssessmentPreparationData.fromJson(Map<String, dynamic> json) =>
      _$AssessmentPreparationDataFromJson(json);

  /// 企业ID
  @override
  final String? enterpriseId;

  /// 统一社会信用代码
  @override
  final String? creditCode;

  /// 匹配ID
  @override
  final String id;

  /// 匹配状态
  @override
  final int matchStatus;

  /// Create a copy of AssessmentPreparationData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$AssessmentPreparationDataCopyWith<_AssessmentPreparationData>
      get copyWith =>
          __$AssessmentPreparationDataCopyWithImpl<_AssessmentPreparationData>(
              this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$AssessmentPreparationDataToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _AssessmentPreparationData &&
            (identical(other.enterpriseId, enterpriseId) ||
                other.enterpriseId == enterpriseId) &&
            (identical(other.creditCode, creditCode) ||
                other.creditCode == creditCode) &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.matchStatus, matchStatus) ||
                other.matchStatus == matchStatus));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, enterpriseId, creditCode, id, matchStatus);

  @override
  String toString() {
    return 'AssessmentPreparationData(enterpriseId: $enterpriseId, creditCode: $creditCode, id: $id, matchStatus: $matchStatus)';
  }
}

/// @nodoc
abstract mixin class _$AssessmentPreparationDataCopyWith<$Res>
    implements $AssessmentPreparationDataCopyWith<$Res> {
  factory _$AssessmentPreparationDataCopyWith(_AssessmentPreparationData value,
          $Res Function(_AssessmentPreparationData) _then) =
      __$AssessmentPreparationDataCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String? enterpriseId, String? creditCode, String id, int matchStatus});
}

/// @nodoc
class __$AssessmentPreparationDataCopyWithImpl<$Res>
    implements _$AssessmentPreparationDataCopyWith<$Res> {
  __$AssessmentPreparationDataCopyWithImpl(this._self, this._then);

  final _AssessmentPreparationData _self;
  final $Res Function(_AssessmentPreparationData) _then;

  /// Create a copy of AssessmentPreparationData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? enterpriseId = freezed,
    Object? creditCode = freezed,
    Object? id = null,
    Object? matchStatus = null,
  }) {
    return _then(_AssessmentPreparationData(
      enterpriseId: freezed == enterpriseId
          ? _self.enterpriseId
          : enterpriseId // ignore: cast_nullable_to_non_nullable
              as String?,
      creditCode: freezed == creditCode
          ? _self.creditCode
          : creditCode // ignore: cast_nullable_to_non_nullable
              as String?,
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      matchStatus: null == matchStatus
          ? _self.matchStatus
          : matchStatus // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

// dart format on
