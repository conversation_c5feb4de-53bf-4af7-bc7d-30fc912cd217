import 'package:flutter/material.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:zrreport/common/index.dart';
import 'package:zrreport/pages/product/quiz/index.dart';
import '../models/quiz_preparation_models.dart';
import '../services/quiz_preparation_service.dart';

part 'quiz_preparation_provider.g.dart';
part 'quiz_preparation_provider.freezed.dart';

/// 答题准备页面状态
@freezed
abstract class QuizPreparationState with _$QuizPreparationState {
  const factory QuizPreparationState({
    /// 企业名称
    @Default('') String enterpriseName,

    /// 统一社会信用代码
    @Default('') String creditCode,

    /// 申请人身份
    @Default('') String personStand,

    /// 申请人年龄
    @Default('') String age,

    /// 地区ID
    @Default('') String areaId,

    /// 是否正在提交
    @Default(false) bool isSubmitting,
  }) = _QuizPreparationState;
}

/// 答题准备页面状态管理
@riverpod
class QuizPreparationNotifier extends _$QuizPreparationNotifier {
  @override
  QuizPreparationState build() {
    return const QuizPreparationState();
  }

  /// 使用参数初始化状态
  void initializeWithParams(AssessmentPreparationParams params) {
    state = state.copyWith(
      enterpriseName: params.enterpriseName,
      creditCode: params.creditCode,
      age: params.age,
      areaId: params.areaId,
      personStand: params.personStand,
    );
  }

  Future<bool> checkHasAvailableCount() async {
    final service = ref.read(quizPreparationServiceProvider);
    final response = await service.checkMatchCount();
    if (response.code == 200 && response.data != null) {
      bool result = (response.data ?? 0) > 0;
      if (!result) {
        Loading.error("您的评估次数已用完，请明天再来");
      }
      return result;
    } else {
      Loading.error(
          response.message.isNotEmpty ? response.message : '获取可用次数失败');
      return false;
    }
  }

  /// 开始评估
  Future<void> startAssessment(BuildContext context) async {

    state = state.copyWith(isSubmitting: true);
    if (!(await checkHasAvailableCount())) {
      state = state.copyWith(isSubmitting: false);
      return;
    }


    try {
      final request = AssessmentPreparationRequest(
        personStand: state.personStand,
        age: state.age,
        creditCode: state.creditCode,
        enterpriseName: state.enterpriseName,
        areaId: state.areaId.isEmpty ? "20" : state.areaId, // 默认地区ID
      );

      final service = ref.read(quizPreparationServiceProvider);
      final response = await service.createQuestionMatch(request);

      if (response.code == 200 && response.data != null) {
        // 跳转到答题页面
        if (context.mounted) {
          Navigator.of(context).pushReplacement(
            MaterialPageRoute(
              builder: (context) => QuizPage(
                matchId: response.data!.id,
                matchType: "1",
              ),
            ),
          );
        }
      } else {
        // 使用 Loading.error 显示错误
        final errorMessage =
            response.message.isNotEmpty ? response.message : '请求失败，请重试';
        if (context.mounted) {
          Loading.error(errorMessage);
        }
      }
    } catch (e) {
      // 使用 Loading.error 显示错误
      if (context.mounted) {
        Loading.error('网络错误，请检查网络连接后重试');
      }
    } finally {
      state = state.copyWith(isSubmitting: false);
    }
  }
}
