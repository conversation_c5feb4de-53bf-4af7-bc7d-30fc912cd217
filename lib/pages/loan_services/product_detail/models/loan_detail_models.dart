import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:zrreport/common/index.dart';

part 'loan_detail_models.freezed.dart';
part 'loan_detail_models.g.dart';

/// 贷款商品详情数据模型
@freezed
abstract class ProductDetailModel with _$ProductDetailModel {
  const factory ProductDetailModel({
    required String id,
    String? cateId,
    required String bankId,
    required String adminId,
    required String deptId,
    String? pic,
    required String name,
    String? productSn,
    required int type,
    String? keywords,
    required String bankName,
    required int minQuota,
    required int maxQuota,
    required String minAnnualRate,
    required String maxAnnualRate,
    required String loanTerm,
    required String repaymentType,
    String? repaymentName,
    String? advantageDataJson,
    required int loanType,
    String? legalApplyRequire,
    String? companyTaxInfo,
    String? companyBizStatus,
    String? companyBankFlow,
    String? corporateAge,
    String? corporateMaxAge,
    String? corporateChangeTime,
    String? corporateSharesRatio,
    String? companyCreateTime,
    String? companyTaxRating,
    String? companyTaxStatus,
    String? companyReceiptRequire,
    required int accessAreaType,
    required int publishStatus,
    String? verifyStatus,
    String? note,
    required String createTime,
    String? updateBy,
    String? matchType,
    String? creditRequire,
    String? otherRequire,
    String? corporateRequire,
    String? companyApplyRequire,
    String? accessIndustry,
    String? prohibitIndustry,
    String? requiredMaterials,
    String? applyDesc,
    String? channelCode,
    String? intoElement,
    @Default([]) List<ForbidIndustryModel> forbidIndustryList,
    @Default([]) List<CustomRequireModel> customRequireList,
    @Default([]) List<dynamic> treeSelect,
    @Default(false) bool haveQuestion,
  }) = _ProductDetailModel;

  factory ProductDetailModel.fromJson(Map<String, dynamic> json) =>
      _$ProductDetailModelFromJson(json);
}

/// 禁入行业模型
@freezed
abstract class ForbidIndustryModel with _$ForbidIndustryModel {
  const factory ForbidIndustryModel({
    required String id,
    required String industryId,
    required String industryCode,
    required String industryName,
    required String relation,
    required int type,
  }) = _ForbidIndustryModel;

  factory ForbidIndustryModel.fromJson(Map<String, dynamic> json) =>
      _$ForbidIndustryModelFromJson(json);
}

/// 自定义要求模型
@freezed
abstract class CustomRequireModel with _$CustomRequireModel {
  const factory CustomRequireModel({
    required String title,
    required String content,
  }) = _CustomRequireModel;

  factory CustomRequireModel.fromJson(Map<String, dynamic> json) =>
      _$CustomRequireModelFromJson(json);
}

/// 贷款商品详情响应类型
typedef ProductDetailResponse = BaseResponse<ProductDetailModel>;

/// 创建贷款商品详情响应的工厂方法
ProductDetailResponse createProductDetailResponse(dynamic json) {
  return BaseResponse.fromJson(
    json,
    (data) => ProductDetailModel.fromJson(data as Map<String, dynamic>),
  );
}

/// 导航项目模型
@freezed
abstract class NavigationItem with _$NavigationItem {
  const factory NavigationItem({
    required String title,
    required String key,
    @Default(false) bool isSelected,
  }) = _NavigationItem;
}

